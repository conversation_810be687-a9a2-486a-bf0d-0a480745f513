<template>
    <div class="h-full pb-1 pr-1">
        <div class="h-full flex flex-col bg-white rounded-lg pb-1">
            <!-- 翻译设置区域 -->
            <div class="p-4">
                <div class="flex items-center space-x-4 flex-wrap">
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">翻译线路:</label>
                        <el-select v-model="translateConfig.vendor" placeholder="请选择供应商" size="small"
                            style="width: 120px" @change="handleVendorChange">
                            <el-option v-for="vendor in docVendors" :key="vendor.value" :label="vendor.label"
                                :value="vendor.value" />
                        </el-select>
                    </div>
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">源语言:</label>
                        <el-select v-model="translateConfig.from" placeholder="请选择源语言" size="small"
                            style="width: 100px">
                            <el-option v-for="lang in sourceLanguages" :key="lang.value" :label="lang.label"
                                :value="lang.value" />
                        </el-select>
                    </div>
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">目标语言:</label>
                        <el-select v-model="translateConfig.to" placeholder="请选择目标语言" size="small" style="width: 100px">
                            <el-option v-for="lang in targetLanguages" :key="lang.value" :label="lang.label"
                                :value="lang.value" />
                        </el-select>
                    </div>
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">垂直领域:</label>
                        <el-select v-model="translateConfig.domain" placeholder="请选择领域" size="small"
                            style="width: 120px">
                            <el-option label="通用领域" value="general" />
                        </el-select>
                    </div>
                </div>
                <div class="flex items-center space-x-4 mt-3 flex-wrap">
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">输出格式:</label>
                        <el-select v-model="translateConfig.outputFormats" placeholder="选择输出格式" size="small"
                            style="width: 150px" multiple collapse-tags>
                            <el-option v-for="format in availableOutputFormats" :key="format.value"
                                :label="format.label" :value="format.value" />
                        </el-select>
                    </div>
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">文件名前缀:</label>
                        <el-input v-model="translateConfig.filenamePrefix" placeholder="例如：translated_" size="small"
                            style="width: 120px" />
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="flex-1 flex p-4 space-x-4 min-h-0">
                <!-- 原文档区域 -->
                <div class="flex-1 bg-white rounded-lg border p-4 flex flex-col">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">原文档</h3>
                    <div class="flex-1 flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg"
                        @drop="handleDrop" @dragover.prevent @dragenter.prevent>
                        <div v-if="!selectedDocument" class="text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none"
                                viewBox="0 0 48 48">
                                <path
                                    d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="mt-4">
                                <label for="file-upload" class="cursor-pointer">
                                    <span class="mt-2 block text-sm font-medium text-gray-900">点击选择文档或拖拽到此处</span>
                                    <span class="mt-1 block text-xs text-gray-500">支持 DOC, DOCX, PDF, TXT, HTML, XML,
                                        PPT, PPTX, XLS, XLSX 格式，最大 50MB</span>
                                </label>
                                <input id="file-upload" name="file-upload" type="file" class="sr-only"
                                    accept=".doc,.docx,.pdf,.txt,.html,.xml,.ppt,.pptx,.xls,.xlsx"
                                    @change="handleFileSelect">
                            </div>
                        </div>
                        <div v-else class="w-full h-full flex flex-col">
                            <div class="flex justify-between items-center mb-2">
                                <div class="flex-1 min-w-0">
                                    <span class="text-sm text-gray-600 truncate block">{{ selectedDocument.name
                                        }}</span>
                                    <span class="text-xs text-gray-400">{{ formatFileSize(selectedDocument.size) }} • {{
                                        getFileFormat(selectedDocument.name) }}</span>
                                </div>
                                <el-button size="small" type="danger" @click="clearDocument">清除</el-button>
                            </div>
                            <div class="flex-1 flex items-center justify-center bg-gray-50 rounded border">
                                <div class="text-center">
                                    <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <p class="mt-2 text-sm text-gray-600">{{ selectedDocument.name }}</p>
                                    <p class="text-xs text-gray-400">文档已选择，点击翻译开始处理</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 翻译按钮区域 -->
                <div class="w-12 flex flex-col items-center justify-center">
                    <el-button type="primary" :loading="translating"
                        :disabled="!selectedDocument || !translateConfig.vendor || !translateConfig.from || !translateConfig.to"
                        @click="handleTranslate" class="w-16 h-16 rounded-full">
                        <template v-if="!translating">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </template>
                    </el-button>
                    <span class="text-xs text-gray-500 mt-2 text-center">{{ translating ? '翻译中...' : '开始翻译' }}</span>
                </div>

                <!-- 任务列表区域 -->
                <div class="flex-1 bg-white rounded-lg border p-4 flex flex-col">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">翻译任务列表</h3>
                        <div class="space-x-2">
                            <el-button size="small" @click="refreshTaskList" :loading="taskListLoading">
                                刷新
                            </el-button>
                            <el-button v-if="selectedTaskId" size="small" @click="queryStatus" :loading="querying">
                                查询状态
                            </el-button>
                        </div>
                    </div>

                    <!-- 任务列表 -->
                    <div class="flex-1 overflow-hidden flex flex-col">
                        <div v-if="taskListLoading && taskList.length === 0"
                            class="flex-1 flex items-center justify-center">
                            <div class="text-center">
                                <el-icon class="animate-spin text-2xl text-blue-500 mb-2">
                                    <Loading />
                                </el-icon>
                                <p class="text-sm text-gray-600">加载任务列表...</p>
                            </div>
                        </div>
                        <div v-else-if="taskList.length === 0" class="flex-1 flex items-center justify-center">
                            <div class="text-center text-gray-400">
                                <svg class="mx-auto h-12 w-12 mb-2" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <p class="mt-2 text-sm">暂无翻译任务</p>
                            </div>
                        </div>
                        <div v-else class="flex-1 overflow-auto">
                            <div class="space-y-2">
                                <div v-for="task in taskList" :key="task.id" :class="[
                                    'border rounded-lg p-3 cursor-pointer transition-all duration-200',
                                    selectedTaskId === task.externalTaskId
                                        ? 'border-blue-500 bg-blue-50'
                                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                                ]" @click="selectTask(task)">
                                    <div class="flex justify-between items-start mb-2">
                                        <div class="flex-1 min-w-0">
                                            <div class="text-sm font-medium truncate">{{ task.originalFilename }}</div>
                                            <div class="text-xs text-gray-500 mt-1">
                                                {{ task.sourceLanguage }} → {{ task.targetLanguage }}
                                                • {{ task.fileFormat?.toUpperCase() }}
                                                • {{ formatFileSize(task.fileSize) }}
                                            </div>
                                        </div>
                                        <el-tag :type="getStatusType(task.status)" size="small">
                                            {{ getStatusText(task.status) }}
                                        </el-tag>
                                    </div>
                                    <div class="text-xs text-gray-400">
                                        创建时间: {{ new Date(task.createdAt).toLocaleString() }}
                                    </div>

                                    <!-- 翻译完成后的下载链接 -->
                                    <div v-if="task.status === 'Succeeded' && task.resultFiles && task.resultFiles.length > 0"
                                        class="mt-2 pt-2 border-t border-gray-100">
                                        <div class="text-xs text-gray-600 mb-1">结果文件:</div>
                                        <div class="space-y-1">
                                            <div v-for="file in task.resultFiles" :key="file.filename"
                                                class="flex items-center justify-between">
                                                <span class="text-xs text-gray-600 truncate flex-1">{{ file.filename
                                                    }}</span>
                                                <el-button v-if="file.url" size="small" type="primary" link
                                                    @click.stop="downloadFile(file.url, file.filename)">
                                                    下载
                                                </el-button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 失败信息 -->
                                    <div v-if="task.status === 'Failed' && task.reason"
                                        class="mt-2 pt-2 border-t border-gray-100">
                                        <div class="text-xs text-red-600">
                                            失败原因: {{ task.reason }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 分页 -->
                        <div v-if="taskListPagination.total > taskListPagination.pageSize"
                            class="mt-4 flex justify-center">
                            <el-pagination v-model:current-page="taskListPagination.current"
                                :page-size="taskListPagination.pageSize" :total="taskListPagination.total"
                                layout="prev, pager, next" small @current-change="handlePageChange" />
                        </div>
                    </div>

                    <!-- 选中任务的详细信息 -->
                    <div v-if="selectedTaskId && translationStatus" class="mt-4 pt-4 border-t border-gray-200">
                        <div class="text-sm font-medium text-gray-700 mb-2">任务详情</div>
                        <div class="border rounded p-3 space-y-2 bg-gray-50">
                            <div class="flex justify-between text-xs">
                                <span class="text-gray-600">任务ID:</span>
                                <span class="font-mono">{{ selectedTaskId }}</span>
                            </div>
                            <div class="flex justify-between text-xs">
                                <span class="text-gray-600">状态:</span>
                                <el-tag :type="getStatusType(translationStatus?.status)" size="small">
                                    {{ getStatusText(translationStatus?.status) }}
                                </el-tag>
                            </div>
                            <div v-if="translationStatus?.reason" class="flex justify-between text-xs">
                                <span class="text-gray-600">说明:</span>
                                <span class="text-right flex-1 ml-2">{{ translationStatus.reason }}</span>
                            </div>
                            <div v-if="translationStatus?.created_at" class="flex justify-between text-xs">
                                <span class="text-gray-600">创建时间:</span>
                                <span>{{ formatTime(translationStatus.created_at) }}</span>
                            </div>
                            <div v-if="translationStatus?.updated_at" class="flex justify-between text-xs">
                                <span class="text-gray-600">更新时间:</span>
                                <span>{{ formatTime(translationStatus.updated_at) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, onUnmounted } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import {
    translateDocumentAPI,
    queryDocumentTranslationAPI,
    getLanguagesAPI,
    VendorType,
    type ITranslateDocumentParams,
    type IQueryDocumentParams,
    type IQueryDocumentResponse
} from '@/api/translation'
import axios from 'axios'
import { $message } from '@/utils/message'

// 响应式数据
const selectedDocument = ref<File | null>(null)
const translating = ref(false)
const querying = ref(false)
const autoQuerying = ref(false)
const translationTask = ref<{ taskId: string } | null>(null)
const translationStatus = ref<IQueryDocumentResponse | null>(null)
let autoQueryTimer: NodeJS.Timeout | null = null

// 任务列表相关数据
const taskList = ref<any[]>([])
const taskListLoading = ref(false)
const selectedTaskId = ref<string | null>(null)
const taskListPagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0
})

// 翻译配置
const translateConfig = reactive({
    vendor: VendorType.BAIDU as VendorType,
    from: 'auto',
    to: 'zh',
    domain: 'general',
    outputFormats: [] as string[],
    filenamePrefix: ''
})

// 语言选项
const sourceLanguages = ref<Array<{ label: string; value: string }>>([])
const targetLanguages = ref<Array<{ label: string; value: string }>>([])

// 文档翻译供应商选项（目前只支持百度）
const docVendors = computed(() => [
    { label: '百度翻译', value: VendorType.BAIDU }
])

// 可用的输出格式选项
const availableOutputFormats = computed(() => {
    if (!selectedDocument.value) return []

    const format = getFileFormat(selectedDocument.value.name)
    const formatMap: { [key: string]: Array<{ label: string; value: string }> } = {
        'doc': [
            { label: 'DOCX', value: 'docx' },
            { label: 'PDF', value: 'pdf' }
        ],
        'docx': [
            { label: 'DOCX', value: 'docx' },
            { label: 'PDF', value: 'pdf' }
        ],
        'pdf': [
            { label: 'DOCX', value: 'docx' },
            { label: 'PDF', value: 'pdf' }
        ],
        'txt': [
            { label: 'TXT', value: 'txt' }
        ],
        'html': [
            { label: 'HTML', value: 'html' }
        ],
        'xml': [
            { label: 'XML', value: 'xml' }
        ],
        'ppt': [
            { label: 'PPTX', value: 'pptx' }
        ],
        'pptx': [
            { label: 'PPTX', value: 'pptx' }
        ],
        'xls': [
            { label: 'XLSX', value: 'xlsx' }
        ],
        'xlsx': [
            { label: 'XLSX', value: 'xlsx' }
        ]
    }

    return formatMap[format] || []
})

// 文件选择处理
const handleFileSelect = (event: Event) => {
    const target = event.target as HTMLInputElement
    const file = target.files?.[0]
    if (file) {
        handleDocumentFile(file)
    }
}

// 拖拽处理
const handleDrop = (event: DragEvent) => {
    event.preventDefault()
    const files = event.dataTransfer?.files
    if (files && files.length > 0) {
        const file = files[0]
        if (isValidDocumentFile(file)) {
            handleDocumentFile(file)
        } else {
            $message.error('请选择支持的文档格式')
        }
    }
}

// 处理文档文件
const handleDocumentFile = (file: File) => {
    // 检查文件大小（50MB限制）
    if (file.size > 50 * 1024 * 1024) {
        $message.error('文档文件大小不能超过 50MB')
        return
    }

    // 检查文件类型
    if (!isValidDocumentFile(file)) {
        $message.error('不支持的文档格式，请选择 DOC、DOCX、PDF、TXT、HTML、XML、PPT、PPTX、XLS、XLSX 格式的文档')
        return
    }

    selectedDocument.value = file

    // 清除之前的翻译结果
    translationTask.value = null
    translationStatus.value = null

    // 清除输出格式选择
    translateConfig.outputFormats = []
}

// 验证文档文件类型
const isValidDocumentFile = (file: File): boolean => {
    const allowedTypes = [
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/pdf',
        'text/plain',
        'text/html',
        'application/xml',
        'text/xml',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ]

    const allowedExtensions = ['.doc', '.docx', '.pdf', '.txt', '.html', '.xml', '.ppt', '.pptx', '.xls', '.xlsx']
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()

    return allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension)
}

// 获取文件格式
const getFileFormat = (filename: string): string => {
    const ext = filename.split('.').pop()?.toLowerCase() || ''
    return ext
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 清除文档
const clearDocument = () => {
    selectedDocument.value = null
    translationTask.value = null
    translationStatus.value = null
    translateConfig.outputFormats = []

    // 清除文件输入框
    const fileInput = document.getElementById('file-upload') as HTMLInputElement
    if (fileInput) {
        fileInput.value = ''
    }
}

// 供应商变更处理
const handleVendorChange = async () => {
    try {
        await loadLanguages()
    } catch (error) {
        console.error('加载语言列表失败:', error)
    }
}

// 加载语言列表
const loadLanguages = async () => {
    if (!translateConfig.vendor) return

    try {
        const [sourceRes, targetRes] = await Promise.all([
            getLanguagesAPI(translateConfig.vendor, 'source'),
            getLanguagesAPI(translateConfig.vendor, 'target')
        ])

        sourceLanguages.value = Object.entries(sourceRes.data).map(([value, label]) => ({
            label: label as string,
            value
        }))

        targetLanguages.value = Object.entries(targetRes.data).map(([value, label]) => ({
            label: label as string,
            value
        }))

        // 设置默认语言
        if (sourceLanguages.value.length > 0 && !translateConfig.from) {
            translateConfig.from = sourceLanguages.value[0].value
        }
        if (targetLanguages.value.length > 0 && !translateConfig.to) {
            translateConfig.to = targetLanguages.value[0].value
        }
    } catch (error) {
        console.error('获取语言列表失败:', error)
        $message.error('获取语言列表失败')
    }
}

// 执行翻译
const handleTranslate = async () => {
    if (!selectedDocument.value) {
        $message.error('请先选择文档')
        return
    }

    if (!translateConfig.vendor || !translateConfig.from || !translateConfig.to) {
        $message.error('请完善翻译配置')
        return
    }

    translating.value = true
    translationTask.value = null
    translationStatus.value = null

    try {
        // 将文档转换为 base64
        const documentBase64 = await fileToBase64(selectedDocument.value)

        // 调用翻译API
        const params: ITranslateDocumentParams = {
            documentBase64: documentBase64.split(',')[1], // 去掉data:前缀
            from: translateConfig.from,
            to: translateConfig.to,
            format: getFileFormat(selectedDocument.value.name),
            filename: selectedDocument.value.name,
            vendor: translateConfig.vendor,
            domain: translateConfig.domain || undefined,
            outputFormats: translateConfig.outputFormats.length > 0 ? translateConfig.outputFormats : undefined,
            filenamePrefix: translateConfig.filenamePrefix || undefined
        }

        const response = await translateDocumentAPI(params)
        console.log('翻译任务创建响应:', response)

        translationTask.value = { taskId: response.data.taskId }
        selectedTaskId.value = response.data.taskId
        $message.success('翻译任务创建成功，正在处理中...')

        // 刷新任务列表
        await loadTaskList()

        // 自动开始查询状态
        setTimeout(() => {
            queryStatus()
        }, 2000)
    } catch (error: any) {
        console.error('创建翻译任务失败:', error)
        const errorMessage = error?.response?.data?.message || error?.message || '创建翻译任务失败，请稍后重试'
        $message.error(errorMessage)
    } finally {
        translating.value = false
    }
}

// 查询翻译状态
const queryStatus = async () => {
    if (!translationTask.value?.taskId) {
        $message.error('没有可查询的翻译任务')
        return
    }

    querying.value = true

    try {
        const params: IQueryDocumentParams = {
            taskId: translationTask.value.taskId,
            vendor: translateConfig.vendor
        }

        const response = await queryDocumentTranslationAPI(params)
        console.log('查询状态响应:', response)

        translationStatus.value = response.data

        // 根据状态显示不同的消息
        const status = response.data.status
        if (status === 'Succeeded') {
            $message.success('翻译完成！')
            // 停止自动查询
            if (autoQuerying.value) {
                toggleAutoQuery()
            }
        } else if (status === 'Failed') {
            $message.error(`翻译失败: ${response.data.reason || '未知错误'}`)
            // 停止自动查询
            if (autoQuerying.value) {
                toggleAutoQuery()
            }
        } else if (status === 'Running') {
            $message.info('翻译正在进行中...')
        } else if (status === 'NotStarted') {
            $message.info('翻译任务已创建，等待开始...')
        } else if (status === 'Expired') {
            $message.warning('翻译任务已过期')
            // 停止自动查询
            if (autoQuerying.value) {
                toggleAutoQuery()
            }
        }
    } catch (error: any) {
        console.error('查询翻译状态失败:', error)
        const errorMessage = error?.response?.data?.message || error?.message || '查询翻译状态失败，请稍后重试'
        $message.error(errorMessage)
    } finally {
        querying.value = false
    }
}

// 切换自动查询
const toggleAutoQuery = () => {
    if (autoQuerying.value) {
        // 停止自动查询
        if (autoQueryTimer) {
            clearInterval(autoQueryTimer)
            autoQueryTimer = null
        }
        autoQuerying.value = false
        $message.info('已停止自动查询')
    } else {
        // 开始自动查询
        if (!translationTask.value?.taskId) {
            $message.error('没有可查询的翻译任务')
            return
        }

        autoQuerying.value = true
        autoQueryTimer = setInterval(() => {
            queryStatus()
        }, 5000) // 每5秒查询一次
        $message.info('已开始自动查询，每5秒查询一次状态')
    }
}

// 获取状态类型（用于标签颜色）
const getStatusType = (status?: string) => {
    switch (status) {
        case 'Succeeded':
            return 'success'
        case 'Failed':
            return 'danger'
        case 'Running':
            return 'primary'
        case 'NotStarted':
            return 'warning'
        case 'Expired':
            return 'info'
        default:
            return 'info'
    }
}

// 获取状态文本
const getStatusText = (status?: string) => {
    switch (status) {
        case 'Succeeded':
            return '翻译完成'
        case 'Failed':
            return '翻译失败'
        case 'Running':
            return '翻译中'
        case 'NotStarted':
            return '等待开始'
        case 'Expired':
            return '已过期'
        default:
            return '未知状态'
    }
}

// 格式化时间
const formatTime = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString()
}

// 下载文件
const downloadFile = async (url: string, filename: string) => {
    try {
        // 创建一个临时的a标签来下载文件
        const link = document.createElement('a')
        link.href = url
        link.download = filename
        link.target = '_blank'

        // 对于跨域文件，我们需要先获取blob
        const response = await fetch(url)
        const blob = await response.blob()
        const blobUrl = window.URL.createObjectURL(blob)

        link.href = blobUrl
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 清理blob URL
        window.URL.revokeObjectURL(blobUrl)

        $message.success('文件下载成功')
    } catch (error) {
        console.error('文件下载失败:', error)
        // 降级方案：直接打开文件链接
        window.open(url, '_blank')
        $message.info('已在新窗口打开文件，请右键保存')
    }
}

// 文件转 base64
const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = () => {
            const result = reader.result as string
            resolve(result)
        }
        reader.onerror = () => {
            reject(new Error('文件读取失败'))
        }
        reader.readAsDataURL(file)
    })
}

// 获取任务列表
const loadTaskList = async (page: number = 1) => {
    taskListLoading.value = true
    try {
        const response = await axios.get('/api/client/translation/document-tasks', {
            params: {
                limit: taskListPagination.pageSize,
                offset: (page - 1) * taskListPagination.pageSize
            }
        })

        if (response.data.code === 200) {
            taskList.value = response.data.data.tasks
            taskListPagination.total = response.data.data.total
            taskListPagination.current = page
        }
    } catch (error) {
        console.error('获取任务列表失败:', error)
        $message.error('获取任务列表失败')
    } finally {
        taskListLoading.value = false
    }
}

// 选择任务
const selectTask = (task: any) => {
    selectedTaskId.value = task.externalTaskId
    translationTask.value = { taskId: task.externalTaskId }

    // 查询选中任务的状态
    queryTaskStatus(task.externalTaskId)
}

// 查询指定任务的状态
const queryTaskStatus = async (taskId: string) => {
    querying.value = true
    try {
        const params: IQueryDocumentParams = {
            taskId: taskId,
            vendor: translateConfig.vendor
        }

        const response = await queryDocumentTranslationAPI(params)
        console.log('查询状态响应:', response)

        translationStatus.value = response.data

        // 根据状态显示不同的消息
        const status = response.data.status
        if (status === 'Succeeded') {
            $message.success('翻译完成！')
        } else if (status === 'Failed') {
            $message.error(`翻译失败: ${response.data.reason || '未知错误'}`)
        } else if (status === 'Running') {
            $message.info('翻译正在进行中...')
        } else if (status === 'NotStarted') {
            $message.info('翻译任务已创建，等待开始...')
        } else if (status === 'Expired') {
            $message.warning('翻译任务已过期')
        }
    } catch (error: any) {
        console.error('查询翻译状态失败:', error)
        const errorMessage = error?.response?.data?.message || error?.message || '查询翻译状态失败，请稍后重试'
        $message.error(errorMessage)
    } finally {
        querying.value = false
    }
}

// 刷新任务列表
const refreshTaskList = () => {
    loadTaskList(taskListPagination.current)
}

// 分页改变
const handlePageChange = (page: number) => {
    loadTaskList(page)
}

// 组件挂载时初始化
onMounted(async () => {
    await loadLanguages()
    await loadTaskList()
})

// 组件卸载时清理定时器
onUnmounted(() => {
    if (autoQueryTimer) {
        clearInterval(autoQueryTimer)
        autoQueryTimer = null
    }
})
</script>