<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百度图片翻译功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h2 {
            color: #2c3e50;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            min-height: 100px;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.loading {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .image-preview {
            max-width: 100%;
            max-height: 300px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .test-info h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✓ ";
            color: #27ae60;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 百度图片翻译功能测试</h1>
        
        <div class="test-info">
            <h3>📋 测试说明</h3>
            <p>本页面用于测试新接入的百度图片翻译功能。百度图片翻译基于业界领先的深度学习技术，提供多场景、多语种、高精度的整图识别+翻译服务。</p>
            
            <h4>🎯 支持的功能特性：</h4>
            <ul class="feature-list">
                <li>支持多种图片格式：JPG、PNG、WebP</li>
                <li>支持多种语言翻译：中文、英文、日语、韩语等20+种语言</li>
                <li>返回翻译后的图片（base64格式）</li>
                <li>支持图片贴合功能</li>
                <li>智能识别图片中的文字并进行翻译</li>
            </ul>
            
            <h4>📝 测试步骤：</h4>
            <ol>
                <li>配置百度翻译API密钥</li>
                <li>选择源语言和目标语言</li>
                <li>上传包含文字的图片</li>
                <li>点击"开始翻译"按钮</li>
                <li>查看翻译结果和翻译后的图片</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>🔧 API配置测试</h2>
            <div class="form-group">
                <label for="apiKey">百度API Key:</label>
                <input type="text" id="apiKey" placeholder="请输入百度翻译API Key">
            </div>
            <div class="form-group">
                <label for="secretKey">百度Secret Key:</label>
                <input type="password" id="secretKey" placeholder="请输入百度翻译Secret Key">
            </div>
            <button onclick="testApiConfig()">测试API配置</button>
            <div id="apiResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>🖼️ 图片翻译测试</h2>
            <div class="form-group">
                <label for="sourceLanguage">源语言:</label>
                <select id="sourceLanguage">
                    <option value="zh">中文</option>
                    <option value="en">英文</option>
                    <option value="jp">日语</option>
                    <option value="kor">韩语</option>
                    <option value="fra">法语</option>
                    <option value="de">德语</option>
                    <option value="spa">西班牙语</option>
                    <option value="ru">俄语</option>
                </select>
            </div>
            <div class="form-group">
                <label for="targetLanguage">目标语言:</label>
                <select id="targetLanguage">
                    <option value="en">英文</option>
                    <option value="zh">中文</option>
                    <option value="jp">日语</option>
                    <option value="kor">韩语</option>
                    <option value="fra">法语</option>
                    <option value="de">德语</option>
                    <option value="spa">西班牙语</option>
                    <option value="ru">俄语</option>
                </select>
            </div>
            <div class="form-group">
                <label for="imageFile">选择图片:</label>
                <input type="file" id="imageFile" accept="image/*" onchange="previewImage()">
            </div>
            <div id="imagePreview"></div>
            <button onclick="translateImage()" id="translateBtn">开始翻译</button>
            <div id="translationResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>📊 测试用例</h2>
            <p>以下是一些推荐的测试用例：</p>
            <ul>
                <li><strong>中文→英文：</strong>上传包含中文文字的图片，翻译为英文</li>
                <li><strong>英文→中文：</strong>上传包含英文文字的图片，翻译为中文</li>
                <li><strong>多行文本：</strong>测试包含多行文字的图片翻译</li>
                <li><strong>复杂背景：</strong>测试背景复杂的图片中的文字识别和翻译</li>
                <li><strong>不同字体：</strong>测试不同字体和大小的文字翻译</li>
            </ul>
        </div>
    </div>

    <script>
        // 测试API配置
        async function testApiConfig() {
            const apiKey = document.getElementById('apiKey').value;
            const secretKey = document.getElementById('secretKey').value;
            const resultDiv = document.getElementById('apiResult');
            
            if (!apiKey || !secretKey) {
                showResult(resultDiv, 'error', '请输入API Key和Secret Key');
                return;
            }
            
            showResult(resultDiv, 'loading', '正在测试API配置...');
            
            try {
                // 测试获取access_token
                const response = await fetch('https://aip.baidubce.com/oauth/2.0/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        grant_type: 'client_credentials',
                        client_id: apiKey,
                        client_secret: secretKey
                    })
                });
                
                const data = await response.json();
                
                if (data.access_token) {
                    showResult(resultDiv, 'success', `API配置正确！Access Token: ${data.access_token.substring(0, 20)}...`);
                } else {
                    showResult(resultDiv, 'error', `API配置错误：${data.error_description || data.error || '未知错误'}`);
                }
            } catch (error) {
                showResult(resultDiv, 'error', `网络错误：${error.message}`);
            }
        }
        
        // 预览图片
        function previewImage() {
            const fileInput = document.getElementById('imageFile');
            const previewDiv = document.getElementById('imagePreview');
            
            if (fileInput.files && fileInput.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewDiv.innerHTML = `<img src="${e.target.result}" class="image-preview" alt="预览图片">`;
                };
                reader.readAsDataURL(fileInput.files[0]);
            }
        }
        
        // 翻译图片
        async function translateImage() {
            const apiKey = document.getElementById('apiKey').value;
            const secretKey = document.getElementById('secretKey').value;
            const sourceLanguage = document.getElementById('sourceLanguage').value;
            const targetLanguage = document.getElementById('targetLanguage').value;
            const fileInput = document.getElementById('imageFile');
            const resultDiv = document.getElementById('translationResult');
            const translateBtn = document.getElementById('translateBtn');
            
            if (!apiKey || !secretKey) {
                showResult(resultDiv, 'error', '请先配置API Key和Secret Key');
                return;
            }
            
            if (!fileInput.files || !fileInput.files[0]) {
                showResult(resultDiv, 'error', '请选择要翻译的图片');
                return;
            }
            
            translateBtn.disabled = true;
            showResult(resultDiv, 'loading', '正在翻译图片，请稍候...');
            
            try {
                // 模拟调用后端API
                const formData = new FormData();
                formData.append('imageBase64', await fileToBase64(fileInput.files[0]));
                formData.append('from', sourceLanguage);
                formData.append('to', targetLanguage);
                formData.append('vendor', 'baidu');
                
                // 这里应该调用实际的后端API
                // const response = await fetch('/api/client/translation/translate-image', {
                //     method: 'POST',
                //     body: formData
                // });
                
                // 模拟成功响应
                setTimeout(() => {
                    showResult(resultDiv, 'success', 
                        `翻译完成！<br>
                        <strong>源语言：</strong>${sourceLanguage}<br>
                        <strong>目标语言：</strong>${targetLanguage}<br>
                        <strong>结果：</strong>图片翻译完成。翻译后的图片: data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...<br>
                        <em>注意：这是模拟结果，实际使用时需要配置正确的API密钥并连接到后端服务。</em>`
                    );
                    translateBtn.disabled = false;
                }, 3000);
                
            } catch (error) {
                showResult(resultDiv, 'error', `翻译失败：${error.message}`);
                translateBtn.disabled = false;
            }
        }
        
        // 文件转base64
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
            });
        }
        
        // 显示结果
        function showResult(element, type, message) {
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.innerHTML = message;
        }
    </script>
</body>
</html>
