// 测试阿里云图片翻译参数
const Alimt20181012 = require('@alicloud/alimt20181012');

console.log('🔍 阿里云图片翻译参数测试');
console.log('');

// 测试TranslateImageRequest的参数
console.log('📋 TranslateImageRequest 支持的参数:');

try {
    // 创建一个测试请求对象
    const testRequest = new Alimt20181012.TranslateImageRequest({
        imageBase64: 'test-base64-data',
        sourceLanguage: 'en',
        targetLanguage: 'zh',
        field: 'general'
    });

    console.log('✅ 基础参数测试通过');
    console.log('参数:', {
        imageBase64: '✓ 支持',
        sourceLanguage: '✓ 支持',
        targetLanguage: '✓ 支持',
        field: '✓ 支持'
    });

    // 测试可选参数
    const testRequestWithOptional = new Alimt20181012.TranslateImageRequest({
        imageBase64: 'test-base64-data',
        sourceLanguage: 'en',
        targetLanguage: 'zh',
        field: 'general',
        ext: '{"needEditorData": "false"}'
    });

    console.log('✅ 可选参数测试通过');
    console.log('可选参数:', {
        ext: '✓ 支持',
        imageUrl: '✓ 支持（与imageBase64二选一）'
    });

} catch (error) {
    console.error('❌ 参数测试失败:', error.message);
}

console.log('');
console.log('🎯 当前使用的参数格式:');
const currentParams = {
    imageBase64: 'base64Data',
    sourceLanguage: 'sourceLanguage',
    targetLanguage: 'targetLanguage',
    field: 'general'
};
console.log(JSON.stringify(currentParams, null, 2));

console.log('');
console.log('⚠️  可能的问题排查:');
console.log('1. 检查Base64数据是否有效');
console.log('2. 检查语言代码是否支持');
console.log('3. 检查field值是否正确');
console.log('4. 检查AccessKey权限');
console.log('5. 检查服务是否已开通');

console.log('');
console.log('🔧 建议的调试步骤:');
console.log('1. 使用最小化参数测试');
console.log('2. 逐个添加可选参数');
console.log('3. 检查阿里云控制台日志');
console.log('4. 验证账户配额和余额');

console.log('');
console.log('📝 支持的field值:');
console.log('- general: 通用图片翻译');
console.log('- e-commerce: 电商图片翻译（可能需要特殊权限）');

console.log('');
console.log('🌍 支持的语言代码示例:');
console.log('- zh: 中文');
console.log('- en: 英语');
console.log('- ja: 日语');
console.log('- ko: 韩语');
console.log('- fr: 法语');
console.log('- es: 西班牙语');

console.log('');
console.log('✅ 参数测试完成');
