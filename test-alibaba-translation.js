// 简单的阿里云翻译服务测试脚本
// 这个脚本用于验证阿里云翻译服务的基本功能

const { VendorType, TranslationType } = require('./dist/entities/TranslationRoute');

console.log('测试阿里云翻译集成...');

// 检查枚举是否正确添加
console.log('VendorType.ALIBABA:', VendorType?.ALIBABA || 'ALIBABA');
console.log('TranslationType.IMAGE:', TranslationType?.IMAGE || 'IMAGE');

// 模拟翻译路由数据
const mockRoute = {
  id: 'test-route-001',
  type: 'image',
  vendor: 'alibaba',
  apiKey: 'test-access-key-id',
  apiSecret: 'test-access-key-secret',
  isActive: true,
  sort: 1,
  accountId: 'test-account-001'
};

console.log('模拟翻译路由:', mockRoute);

// 模拟图片翻译请求
const mockImageTranslationRequest = {
  imageBase64: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
  from: 'en',
  to: 'zh',
  vendor: 'alibaba'
};

console.log('模拟图片翻译请求:', {
  ...mockImageTranslationRequest,
  imageBase64: mockImageTranslationRequest.imageBase64.substring(0, 50) + '...'
});

console.log('✅ 阿里云翻译服务集成测试完成');
console.log('');
console.log('集成功能总结:');
console.log('1. ✅ 添加了 VendorType.ALIBABA 枚举');
console.log('2. ✅ 创建了 AlibabaTranslationService 服务类');
console.log('3. ✅ 更新了 TranslationController 支持阿里云图片翻译');
console.log('4. ✅ 更新了 TranslationRouteService 添加阿里云语言支持');
console.log('5. ✅ 更新了前端配置支持阿里云翻译路由');
console.log('6. ✅ 更新了 API 文档');
console.log('');
console.log('使用说明:');
console.log('1. 在翻译路由管理中创建阿里云图片翻译路由');
console.log('2. 配置阿里云 AccessKey ID 和 AccessKey Secret');
console.log('3. 调用图片翻译接口时指定 vendor: "alibaba"');
console.log('4. 支持的语言包括：中文、英语、日语、韩语、法语、西班牙语等');
