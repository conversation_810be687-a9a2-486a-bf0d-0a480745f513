import request from '../utils/request'

export enum VendorType {
    GOOGLE = 'google',
    BAIDU = 'baidu',
    YOUDAO = 'youdao',
    ALIBABA = 'alibaba',
    DEEPL = 'deepl'
}

export interface ITranslateParams {
    text: string
    from: string
    to: string
    vendor?: VendorType
}

export interface ITranslateResponse {
    text: string
}

export interface ITranslateImageParams {
    imageBase64: string
    from: string
    to: string
    vendor?: VendorType
}

export interface ITranslateImageResponse {
    text: string
}

export interface ILanguageListResponse {
    [key: string]: string
}

export interface ITranslateDocumentParams {
    documentBase64: string
    from: string
    to: string
    format: string
    filename: string
    vendor?: VendorType
    domain?: string
    outputFormats?: string[]
    filenamePrefix?: string
}

export interface ITranslateDocumentResponse {
    taskId: string
    message: string
}

export interface IQueryDocumentParams {
    taskId: string
    vendor?: VendorType
}

export interface IQueryDocumentResponse {
    id: string
    from: string
    to: string
    domain?: string
    input: {
        format: string
        filename: string
        size: number
        character_count?: number
    }
    output?: {
        files: Array<{
            format: string
            filename: string
            size?: number
            url?: string
        }>
    }
    status: 'NotStarted' | 'Running' | 'Succeeded' | 'Failed' | 'Expired'
    reason: string
    created_at: number
    updated_at: number
    expired_at: number
}

// 执行翻译
export function translateAPI(data: ITranslateParams) {
    return request<ITranslateResponse>({
        url: '/client/translation/translate',
        method: 'POST',
        data
    })
}

// 执行图片翻译
export function translateImageAPI(data: ITranslateImageParams) {
    return request<ITranslateImageResponse>({
        url: '/client/translation/translate-image',
        method: 'POST',
        data,
        timeout: 300000
    })
}

// 执行文档翻译
export function translateDocumentAPI(data: ITranslateDocumentParams) {
    return request<ITranslateDocumentResponse>({
        url: '/client/translation/translate-document',
        method: 'POST',
        data,
        timeout: 60000 // 60秒超时
    })
}

// 查询文档翻译状态
export function queryDocumentTranslationAPI(data: IQueryDocumentParams) {
    return request<IQueryDocumentResponse>({
        url: '/client/translation/query-document',
        method: 'POST',
        data,
        timeout: 30000 // 30秒超时
    })
}

// 获取支持的语言列表
export function getLanguagesAPI(vendor: VendorType, type: 'source' | 'target' = 'source') {
    return request<ILanguageListResponse>({
        url: `/client/translation/languages`,
        method: 'GET',
        params: {
            vendor,
            type
        }
    })
}
