import { Repository } from 'typeorm';
import { AppDataSource } from '../config/database';
import {
  DocumentTranslationTask,
  DocumentTranslationStatus,
} from '../entities/DocumentTranslationTask';

export class DocumentTranslationTaskService {
  private repository: Repository<DocumentTranslationTask>;

  constructor() {
    this.repository = AppDataSource.getRepository(DocumentTranslationTask);
  }

  /**
   * 创建文档翻译任务记录
   */
  async createTask(data: {
    externalTaskId: string;
    accountId: string;
    vendor: string;
    originalFilename: string;
    fileFormat: string;
    fileSize: number;
    sourceLanguage: string;
    targetLanguage: string;
    domain?: string;
    outputFormats?: string[];
    filenamePrefix?: string;
  }): Promise<DocumentTranslationTask> {
    const task = this.repository.create({
      ...data,
      status: DocumentTranslationStatus.NOT_STARTED,
    });

    return await this.repository.save(task);
  }

  /**
   * 根据外部任务ID获取任务
   */
  async getTaskByExternalId(externalTaskId: string): Promise<DocumentTranslationTask | null> {
    return await this.repository.findOne({
      where: { externalTaskId },
      relations: ['account'],
    });
  }

  /**
   * 根据用户ID和外部任务ID获取任务
   */
  async getTaskByAccountAndExternalId(
    accountId: string,
    externalTaskId: string,
  ): Promise<DocumentTranslationTask | null> {
    return await this.repository.findOne({
      where: { accountId, externalTaskId },
      relations: ['account'],
    });
  }

  /**
   * 更新任务状态
   */
  async updateTaskStatus(
    externalTaskId: string,
    status: DocumentTranslationStatus,
    data?: {
      reason?: string;
      resultFiles?: Array<{
        format: string;
        filename: string;
        size?: number;
        url?: string;
      }>;
      characterCount?: number;
      startedAt?: Date;
      completedAt?: Date;
      expiredAt?: Date;
      errorMessage?: string;
      rawResponse?: any;
    },
  ): Promise<DocumentTranslationTask | null> {
    const task = await this.getTaskByExternalId(externalTaskId);
    if (!task) {
      return null;
    }

    // 更新状态
    task.status = status;

    // 更新其他字段
    if (data) {
      if (data.reason !== undefined) task.reason = data.reason;
      if (data.resultFiles !== undefined) task.resultFiles = data.resultFiles;
      if (data.characterCount !== undefined) task.characterCount = data.characterCount;
      if (data.startedAt !== undefined) task.startedAt = data.startedAt;
      if (data.completedAt !== undefined) task.completedAt = data.completedAt;
      if (data.expiredAt !== undefined) task.expiredAt = data.expiredAt;
      if (data.errorMessage !== undefined) task.errorMessage = data.errorMessage;
      if (data.rawResponse !== undefined) task.rawResponse = data.rawResponse;
    }

    // 根据状态自动设置时间
    if (status === DocumentTranslationStatus.RUNNING && !task.startedAt) {
      task.startedAt = new Date();
    }
    if (
      (status === DocumentTranslationStatus.SUCCEEDED ||
        status === DocumentTranslationStatus.FAILED) &&
      !task.completedAt
    ) {
      task.completedAt = new Date();
    }

    return await this.repository.save(task);
  }

  /**
   * 获取用户的文档翻译任务列表
   */
  async getTasksByAccount(
    accountId: string,
    options?: {
      status?: DocumentTranslationStatus;
      limit?: number;
      offset?: number;
      orderBy?: 'createdAt' | 'updatedAt';
      orderDirection?: 'ASC' | 'DESC';
    },
  ): Promise<{ tasks: DocumentTranslationTask[]; total: number }> {
    const queryBuilder = this.repository
      .createQueryBuilder('task')
      .leftJoinAndSelect('task.account', 'account')
      .where('task.accountId = :accountId', { accountId });

    // 状态过滤
    if (options?.status) {
      queryBuilder.andWhere('task.status = :status', { status: options.status });
    }

    // 排序
    const orderBy = options?.orderBy || 'createdAt';
    const orderDirection = options?.orderDirection || 'DESC';
    queryBuilder.orderBy(`task.${orderBy}`, orderDirection);

    // 分页
    if (options?.limit) {
      queryBuilder.limit(options.limit);
    }
    if (options?.offset) {
      queryBuilder.offset(options.offset);
    }

    const [tasks, total] = await queryBuilder.getManyAndCount();

    return { tasks, total };
  }

  /**
   * 获取需要更新状态的任务（运行中的任务）
   */
  async getRunningTasks(): Promise<DocumentTranslationTask[]> {
    return await this.repository.find({
      where: { status: DocumentTranslationStatus.RUNNING },
      relations: ['account'],
    });
  }

  /**
   * 删除过期的任务
   */
  async deleteExpiredTasks(): Promise<number> {
    const result = await this.repository.delete({
      status: DocumentTranslationStatus.EXPIRED,
    });

    return result.affected || 0;
  }

  /**
   * 获取任务统计信息
   */
  async getTaskStatistics(accountId: string): Promise<{
    total: number;
    notStarted: number;
    running: number;
    succeeded: number;
    failed: number;
    expired: number;
  }> {
    const tasks = await this.repository.find({
      where: { accountId },
      select: ['status'],
    });

    const statistics = {
      total: tasks.length,
      notStarted: 0,
      running: 0,
      succeeded: 0,
      failed: 0,
      expired: 0,
    };

    tasks.forEach((task) => {
      switch (task.status) {
        case DocumentTranslationStatus.NOT_STARTED:
          statistics.notStarted++;
          break;
        case DocumentTranslationStatus.RUNNING:
          statistics.running++;
          break;
        case DocumentTranslationStatus.SUCCEEDED:
          statistics.succeeded++;
          break;
        case DocumentTranslationStatus.FAILED:
          statistics.failed++;
          break;
        case DocumentTranslationStatus.EXPIRED:
          statistics.expired++;
          break;
      }
    });

    return statistics;
  }

  /**
   * 批量更新任务状态
   */
  async batchUpdateTaskStatus(
    externalTaskIds: string[],
    status: DocumentTranslationStatus,
    data?: {
      reason?: string;
      errorMessage?: string;
    },
  ): Promise<number> {
    const updateData: any = { status };
    if (data?.reason) updateData.reason = data.reason;
    if (data?.errorMessage) updateData.errorMessage = data.errorMessage;

    const result = await this.repository
      .createQueryBuilder()
      .update()
      .set(updateData)
      .where('externalTaskId IN (:...ids)', { ids: externalTaskIds })
      .execute();

    return result.affected || 0;
  }
}
