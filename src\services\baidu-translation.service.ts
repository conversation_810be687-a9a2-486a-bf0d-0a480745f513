import { TranslationRouteService } from './translation-route.service';
import { DocumentTranslationTaskService } from './document-translation-task.service';
import { TranslationRoute } from '../entities/TranslationRoute';
import { TranslationType, VendorType } from '../entities/TranslationRoute';
import { DocumentTranslationStatus } from '../entities/DocumentTranslationTask';
import axios from 'axios';
import crypto from 'crypto';
import FormData from 'form-data';

interface BaiduTranslationResult {
  from: string;
  to: string;
  trans_result: {
    src: string;
    dst: string;
  }[];
}

interface BaiduImageTranslationResult {
  error_code: string;
  error_msg: string;
  data?: {
    from: string;
    to: string;
    content: Array<{
      src: string;
      dst: string;
      rect: string;
      lineCount: number;
      pasteImg?: string;
      points: Array<{ x: number; y: number }>;
    }>;
    sumSrc: string;
    sumDst: string;
    pasteImg?: string;
  };
}

interface BaiduDocTranslationCreateResult {
  result?: {
    id: string; // 文档翻译任务ID
  };
  log_id: number;
  error_code?: number;
  error_msg?: string;
}

interface BaiduDocTranslationQueryResult {
  result?: {
    data: {
      id: string;
      from: string;
      to: string;
      domain?: string;
      input: {
        format: string;
        filename: string;
        size: number;
        character_count?: number;
      };
      output?: {
        files: Array<{
          format: string;
          filename: string;
          size?: number;
          url?: string;
        }>;
      };
      status: 'NotStarted' | 'Running' | 'Succeeded' | 'Failed' | 'Expired';
      reason: string;
      created_at: number;
      updated_at: number;
      expired_at: number;
    };
  };
  log_id: number;
  error_code?: number;
  error_msg?: string;
}

export class BaiduTranslationService {
  private translationRouteService: TranslationRouteService;
  private documentTaskService: DocumentTranslationTaskService;

  constructor() {
    this.translationRouteService = new TranslationRouteService();
    this.documentTaskService = new DocumentTranslationTaskService();
  }

  /**
   * 获取所有可用的百度翻译线路
   */
  async getAvailableBaiduRoutes(accountId: string): Promise<TranslationRoute[]> {
    const routes = await this.translationRouteService.getRoutesByAccount(accountId, false);
    return routes.filter(
      (route) =>
        route.vendor === VendorType.BAIDU && route.type === TranslationType.TEXT && route.isActive,
    );
  }

  /**
   * 获取所有可用的百度图片翻译线路
   */
  async getAvailableBaiduImageRoutes(accountId: string): Promise<TranslationRoute[]> {
    const routes = await this.translationRouteService.getRoutesByAccount(accountId, false);
    return routes.filter(
      (route) =>
        route.vendor === VendorType.BAIDU && route.type === TranslationType.IMAGE && route.isActive,
    );
  }

  /**
   * 获取所有可用的百度文档翻译线路
   */
  async getAvailableBaiduDocRoutes(accountId: string): Promise<TranslationRoute[]> {
    const routes = await this.translationRouteService.getRoutesByAccount(accountId, false);
    return routes.filter(
      (route) =>
        route.vendor === VendorType.BAIDU &&
        route.type === TranslationType.DOCUMENT &&
        route.isActive,
    );
  }

  /**
   * 生成百度翻译签名
   */
  private generateSign(appId: string, query: string, salt: string, secretKey: string): string {
    const str = appId + query + salt + secretKey;
    return crypto.createHash('md5').update(str).digest('hex');
  }

  /**
   * 转换语言代码为百度图片翻译支持的格式
   */
  private convertLanguageCodeForImage(langCode: string): string {
    const languageMap: { [key: string]: string } = {
      zh: 'zh',
      'zh-cn': 'zh',
      'zh-tw': 'cht',
      cht: 'cht',
      en: 'en',
      ja: 'jp',
      jp: 'jp',
      ko: 'kor',
      kor: 'kor',
      ms: 'may',
      may: 'may',
      th: 'th',
      ar: 'ara',
      ara: 'ara',
      vi: 'vie',
      vie: 'vie',
      hi: 'hi',
      pt: 'pt',
      fr: 'fra',
      fra: 'fra',
      de: 'de',
      it: 'it',
      es: 'spa',
      spa: 'spa',
      ru: 'ru',
      nl: 'nl',
      da: 'dan',
      dan: 'dan',
      sv: 'swe',
      swe: 'swe',
      id: 'id',
      pl: 'pl',
      ro: 'rom',
      rom: 'rom',
      tr: 'tr',
      el: 'el',
      hu: 'hu',
    };

    return languageMap[langCode.toLowerCase()] || langCode;
  }

  /**
   * 执行翻译
   */
  async translate(
    text: string,
    from: string,
    to: string,
    route: TranslationRoute,
  ): Promise<string | null> {
    try {
      const salt = Date.now().toString();
      const sign = this.generateSign(route.apiKey, text, salt, route.apiSecret || '');

      const response = await axios.get<BaiduTranslationResult>(
        'https://fanyi-api.baidu.com/api/trans/vip/translate',
        {
          params: {
            q: text,
            from,
            to,
            appid: route.apiKey,
            salt,
            sign,
          },
        },
      );

      if (response.data.trans_result && response.data.trans_result.length > 0) {
        return response.data.trans_result[0].dst;
      }

      console.error('apiKey:', route.apiKey);
      console.error('apiSecret:', route.apiSecret);
      console.error('百度翻译失败:', response.data);
      return null;
    } catch (error) {
      console.error('百度翻译失败:', error);
      return null;
    }
  }

  /**
   * 执行图片翻译
   */
  async translateImage(
    imageBase64: string,
    from: string,
    to: string,
    route: TranslationRoute,
  ): Promise<string | null> {
    try {
      console.log('开始百度图片翻译，参数:', {
        from,
        to,
        routeId: route.id,
        imageSize: imageBase64.length,
        apiKey: route.apiKey,
      });

      // 获取access_token
      const accessToken = await this.getAccessToken(route.apiKey, route.apiSecret || '');
      if (!accessToken) {
        console.error('获取百度access_token失败');
        return null;
      }

      // 转换语言代码
      const sourceLanguage = this.convertLanguageCodeForImage(from);
      const targetLanguage = this.convertLanguageCodeForImage(to);

      console.log('语言转换:', { from, to, sourceLanguage, targetLanguage });

      // 准备图片数据
      let base64Data = imageBase64;
      if (base64Data.includes(',')) {
        base64Data = base64Data.split(',')[1];
      }

      // 将base64转换为Buffer
      const imageBuffer = Buffer.from(base64Data, 'base64');

      // 创建FormData
      const formData = new FormData();
      formData.append('image', imageBuffer, {
        filename: 'image.jpg',
        contentType: 'image/jpeg',
      });
      formData.append('from', sourceLanguage);
      formData.append('to', targetLanguage);
      formData.append('v', '3');
      formData.append('paste', '1'); // 返回整图贴合

      console.log('准备调用百度图片翻译API...');

      const response = await axios.post<BaiduImageTranslationResult>(
        `https://aip.baidubce.com/file/2.0/mt/pictrans/v1?access_token=${accessToken}`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
          },
          timeout: 60000, // 60秒超时
        },
      );

      console.log('百度图片翻译API调用完成');
      console.log('百度API响应:', {
        error_code: response.data.error_code,
        error_msg: response.data.error_msg,
        hasData: !!response.data.data,
        hasPasteImg: !!response.data.data?.pasteImg,
      });

      if (response.data.error_code === '0' && response.data.data) {
        // 如果有贴合图片，返回图片URL或base64
        if (response.data.data.pasteImg) {
          return `data:image/jpeg;base64,${response.data.data.pasteImg}`;
        }

        // 否则返回文本翻译结果
        if (response.data.data.sumDst) {
          return response.data.data.sumDst;
        }
      }

      console.error('百度图片翻译失败:', response.data);
      return null;
    } catch (error: any) {
      console.error('百度图片翻译失败:', error);
      if (error.response) {
        console.error('响应错误:', error.response.data);
      }
      return null;
    }
  }

  /**
   * 获取百度access_token
   */
  private async getAccessToken(apiKey: string, secretKey: string): Promise<string | null> {
    try {
      const response = await axios.post('https://aip.baidubce.com/oauth/2.0/token', null, {
        params: {
          grant_type: 'client_credentials',
          client_id: apiKey,
          client_secret: secretKey,
        },
      });

      if (response.data.access_token) {
        return response.data.access_token;
      }

      console.error('获取access_token失败:', response.data);
      return null;
    } catch (error) {
      console.error('获取access_token异常:', error);
      return null;
    }
  }

  /**
   * 智能翻译（失败时自动切换线路）
   */
  async smartTranslate(
    text: string,
    from: string,
    to: string,
    accountId: string,
  ): Promise<string | null> {
    const routes = await this.getAvailableBaiduRoutes(accountId);

    if (routes.length === 0) {
      throw new Error('没有可用的翻译线路');
    }

    // 尝试每条线路，直到成功或全部失败
    for (const route of routes) {
      const result = await this.translate(text, from, to, route);
      if (result !== null) {
        return result;
      }
      console.log(`线路 ${route.id} 翻译失败，尝试下一条线路`);
    }

    throw new Error('所有翻译线路均不可用');
  }

  /**
   * 创建文档翻译任务
   */
  async createDocumentTranslation(
    documentBase64: string,
    from: string,
    to: string,
    format: string,
    filename: string,
    route: TranslationRoute,
    domain?: string,
    outputFormats?: string[],
    filenamePrefix?: string,
  ): Promise<string | null> {
    try {
      console.log('开始百度文档翻译，参数:', {
        from,
        to,
        format,
        filename,
        domain,
        outputFormats,
        filenamePrefix,
        routeId: route.id,
        documentSize: documentBase64.length,
        apiKey: route.apiKey,
      });

      // 获取access_token
      const accessToken = await this.getAccessToken(route.apiKey, route.apiSecret || '');
      if (!accessToken) {
        console.error('获取百度access_token失败');
        return null;
      }

      // 准备请求数据
      const requestData: any = {
        from,
        to,
        input: {
          content: documentBase64,
          format,
          filename,
        },
      };

      // 添加可选参数
      if (domain) {
        requestData.domain = domain;
      }

      if (outputFormats && outputFormats.length > 0) {
        requestData.output = {
          formats: outputFormats,
        };
        if (filenamePrefix) {
          requestData.output.filename_prefix = filenamePrefix;
        }
      }

      console.log('准备调用百度文档翻译创建API...');

      const response = await axios.post<BaiduDocTranslationCreateResult>(
        `https://aip.baidubce.com/rpc/2.0/mt/v2/doc-translation/create?access_token=${accessToken}`,
        requestData,
        {
          headers: {
            'Content-Type': 'application/json;charset=utf-8',
          },
          timeout: 60000, // 60秒超时
        },
      );

      console.log('百度文档翻译创建API调用完成');
      console.log('百度API响应:', {
        hasResult: !!response.data.result,
        taskId: response.data.result?.id,
        error_code: response.data.error_code,
        error_msg: response.data.error_msg,
      });

      if (response.data.result?.id) {
        return response.data.result.id;
      }

      console.error('百度文档翻译创建失败:', response.data);
      return null;
    } catch (error: any) {
      console.error('百度文档翻译创建失败:', error);
      if (error.response) {
        console.error('响应错误:', error.response.data);
      }
      return null;
    }
  }

  /**
   * 查询文档翻译任务状态
   */
  async queryDocumentTranslation(
    taskId: string,
    route: TranslationRoute,
  ): Promise<BaiduDocTranslationQueryResult['result'] | null> {
    try {
      console.log('查询百度文档翻译状态，任务ID:', taskId);

      // 获取access_token
      const accessToken = await this.getAccessToken(route.apiKey, route.apiSecret || '');
      if (!accessToken) {
        console.error('获取百度access_token失败');
        return null;
      }

      const response = await axios.post<BaiduDocTranslationQueryResult>(
        `https://aip.baidubce.com/rpc/2.0/mt/v2/doc-translation/query?access_token=${accessToken}`,
        { id: taskId },
        {
          headers: {
            'Content-Type': 'application/json;charset=utf-8',
          },
          timeout: 30000, // 30秒超时
        },
      );

      console.log('百度文档翻译查询API调用完成');
      console.log('查询结果:', {
        hasResult: !!response.data.result,
        status: response.data.result?.data?.status,
        reason: response.data.result?.data?.reason,
        error_code: response.data.error_code,
        error_msg: response.data.error_msg,
      });

      if (response.data.result) {
        return response.data.result;
      }

      console.error('百度文档翻译查询失败:', response.data);
      return null;
    } catch (error: any) {
      console.error('百度文档翻译查询失败:', error);
      if (error.response) {
        console.error('响应错误:', error.response.data);
      }
      return null;
    }
  }

  /**
   * 智能图片翻译（失败时自动切换线路）
   */
  async smartTranslateImage(
    imageBase64: string,
    from: string,
    to: string,
    accountId: string,
  ): Promise<string | null> {
    const routes = await this.getAvailableBaiduImageRoutes(accountId);

    if (routes.length === 0) {
      throw new Error('没有可用的百度图片翻译线路');
    }

    // 尝试每条线路，直到成功或全部失败
    for (const route of routes) {
      const result = await this.translateImage(imageBase64, from, to, route);
      if (result !== null) {
        return result;
      }
      console.log(`百度图片翻译线路 ${route.id} 失败，尝试下一条线路`);
    }

    throw new Error('所有百度图片翻译线路均不可用');
  }

  /**
   * 智能文档翻译（失败时自动切换线路）
   */
  async smartTranslateDocument(
    documentBase64: string,
    from: string,
    to: string,
    format: string,
    filename: string,
    accountId: string,
    domain?: string,
    outputFormats?: string[],
    filenamePrefix?: string,
  ): Promise<{ taskId: string; route: TranslationRoute; dbTaskId: string } | null> {
    const routes = await this.getAvailableBaiduDocRoutes(accountId);

    if (routes.length === 0) {
      throw new Error('没有可用的百度文档翻译线路');
    }

    // 计算文件大小（base64解码后的大小）
    const fileSize = Math.floor((documentBase64.length * 3) / 4);

    // 尝试每条线路，直到成功或全部失败
    for (const route of routes) {
      const taskId = await this.createDocumentTranslation(
        documentBase64,
        from,
        to,
        format,
        filename,
        route,
        domain,
        outputFormats,
        filenamePrefix,
      );

      if (taskId !== null) {
        // 创建数据库记录
        try {
          const dbTask = await this.documentTaskService.createTask({
            externalTaskId: taskId,
            accountId,
            vendor: 'baidu',
            originalFilename: filename,
            fileFormat: format,
            fileSize,
            sourceLanguage: from,
            targetLanguage: to,
            domain,
            outputFormats,
            filenamePrefix,
          });

          console.log(`文档翻译任务已创建: 外部ID=${taskId}, 数据库ID=${dbTask.id}`);

          return { taskId, route, dbTaskId: dbTask.id };
        } catch (error) {
          console.error('创建数据库任务记录失败:', error);
          // 即使数据库记录创建失败，也返回任务ID，不影响翻译流程
          return { taskId, route, dbTaskId: '' };
        }
      }
      console.log(`百度文档翻译线路 ${route.id} 失败，尝试下一条线路`);
    }

    throw new Error('所有百度文档翻译线路均不可用');
  }

  /**
   * 查询文档翻译状态并更新数据库记录
   */
  async queryDocumentTranslationWithUpdate(
    taskId: string,
    route: TranslationRoute,
  ): Promise<BaiduDocTranslationQueryResult['result'] | null> {
    try {
      // 调用原始查询方法
      const result = await this.queryDocumentTranslation(taskId, route);

      if (result?.data) {
        // 更新数据库记录
        const status = this.mapBaiduStatusToDbStatus(result.data.status);

        await this.documentTaskService.updateTaskStatus(taskId, status, {
          reason: result.data.reason,
          resultFiles: result.data.output?.files,
          characterCount: result.data.input.character_count,
          startedAt: result.data.created_at ? new Date(result.data.created_at * 1000) : undefined,
          completedAt: result.data.updated_at ? new Date(result.data.updated_at * 1000) : undefined,
          expiredAt: result.data.expired_at ? new Date(result.data.expired_at * 1000) : undefined,
          rawResponse: result,
        });

        console.log(`文档翻译任务状态已更新: ${taskId} -> ${status}`);
      }

      return result;
    } catch (error) {
      console.error('查询文档翻译状态失败:', error);

      // 更新数据库记录为失败状态
      try {
        await this.documentTaskService.updateTaskStatus(taskId, DocumentTranslationStatus.FAILED, {
          errorMessage: error instanceof Error ? error.message : '查询状态失败',
        });
      } catch (dbError) {
        console.error('更新数据库任务状态失败:', dbError);
      }

      return null;
    }
  }

  /**
   * 映射百度翻译状态到数据库状态
   */
  private mapBaiduStatusToDbStatus(baiduStatus: string): DocumentTranslationStatus {
    switch (baiduStatus) {
      case 'NotStarted':
        return DocumentTranslationStatus.NOT_STARTED;
      case 'Running':
        return DocumentTranslationStatus.RUNNING;
      case 'Succeeded':
        return DocumentTranslationStatus.SUCCEEDED;
      case 'Failed':
        return DocumentTranslationStatus.FAILED;
      case 'Expired':
        return DocumentTranslationStatus.EXPIRED;
      default:
        return DocumentTranslationStatus.NOT_STARTED;
    }
  }

  /**
   * 获取用户的文档翻译任务列表
   */
  async getUserDocumentTasks(
    accountId: string,
    options?: {
      status?: DocumentTranslationStatus;
      limit?: number;
      offset?: number;
    },
  ): Promise<{ tasks: any[]; total: number }> {
    return await this.documentTaskService.getTasksByAccount(accountId, options);
  }

  /**
   * 获取用户的文档翻译统计信息
   */
  async getUserDocumentTaskStatistics(accountId: string): Promise<{
    total: number;
    notStarted: number;
    running: number;
    succeeded: number;
    failed: number;
    expired: number;
  }> {
    return await this.documentTaskService.getTaskStatistics(accountId);
  }
}
