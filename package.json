{"name": "scrm-server", "version": "1.0.0", "description": "SCRM服务端", "main": "src/index.ts", "scripts": {"start": "ts-node src/index.ts", "dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "prepare": "husky install", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm migration:generate -- -d src/config/database.ts", "migration:run": "npm run typeorm migration:run -- -d src/config/database.ts", "migration:revert": "npm run typeorm migration:revert -- -d src/config/database.ts"}, "dependencies": {"@alicloud/alimt20181012": "^1.4.1", "@alicloud/openapi-client": "^0.4.15", "@alicloud/tea-util": "^1.4.10", "@koa/cors": "^4.0.0", "@koa/router": "^12.0.1", "@types/ioredis": "^5.0.0", "@types/koa-router": "^7.4.8", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "class-validator": "^0.14.0", "dotenv": "^16.3.1", "ioredis": "^5.6.0", "jsonwebtoken": "^9.0.2", "koa": "^2.14.2", "koa-bodyparser": "^4.4.1", "koa-router": "^13.0.1", "koa-swagger-decorator": "^1.8.7", "mysql2": "^3.6.5", "nodemailer": "^6.10.0", "reflect-metadata": "^0.1.13", "svg-captcha": "^1.4.0", "svg-to-dataurl": "^1.0.0", "swagger-ui-dist": "^5.20.2", "typeorm": "^0.3.17"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/koa": "^2.13.12", "@types/koa__cors": "^4.0.3", "@types/koa__router": "^12.0.4", "@types/koa-bodyparser": "^4.3.12", "@types/node": "^20.10.4", "@types/nodemailer": "^6.4.17", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^8.0.3", "nodemon": "^3.0.2", "prettier": "^3.1.0", "ts-node": "^10.9.1", "typescript": "^5.3.3"}}