import { MigrationInterface, QueryRunner, Table, TableIndex, TableForeignKey } from 'typeorm';

export class CreateDocumentTranslationTaskTable1640995200000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建文档翻译任务表
    await queryRunner.createTable(
      new Table({
        name: 'document_translation_tasks',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            length: '36',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'uuid',
            comment: '主键ID',
          },
          {
            name: 'externalTaskId',
            type: 'varchar',
            length: '100',
            comment: '外部任务ID（百度翻译返回的任务ID）',
          },
          {
            name: 'accountId',
            type: 'varchar',
            length: '36',
            comment: '用户账号ID',
          },
          {
            name: 'vendor',
            type: 'varchar',
            length: '20',
            default: "'baidu'",
            comment: '翻译供应商',
          },
          {
            name: 'originalFilename',
            type: 'varchar',
            length: '255',
            comment: '原始文件名',
          },
          {
            name: 'fileFormat',
            type: 'varchar',
            length: '10',
            comment: '文件格式',
          },
          {
            name: 'fileSize',
            type: 'bigint',
            comment: '文件大小（字节）',
          },
          {
            name: 'sourceLanguage',
            type: 'varchar',
            length: '10',
            comment: '源语言',
          },
          {
            name: 'targetLanguage',
            type: 'varchar',
            length: '10',
            comment: '目标语言',
          },
          {
            name: 'domain',
            type: 'varchar',
            length: '50',
            isNullable: true,
            comment: '垂直领域',
          },
          {
            name: 'outputFormats',
            type: 'json',
            isNullable: true,
            comment: '输出格式列表',
          },
          {
            name: 'filenamePrefix',
            type: 'varchar',
            length: '100',
            isNullable: true,
            comment: '文件名前缀',
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['NotStarted', 'Running', 'Succeeded', 'Failed', 'Expired'],
            default: "'NotStarted'",
            comment: '任务状态',
          },
          {
            name: 'reason',
            type: 'text',
            isNullable: true,
            comment: '状态说明',
          },
          {
            name: 'resultFiles',
            type: 'json',
            isNullable: true,
            comment: '翻译结果文件信息',
          },
          {
            name: 'characterCount',
            type: 'int',
            isNullable: true,
            comment: '字符数量',
          },
          {
            name: 'startedAt',
            type: 'datetime',
            isNullable: true,
            comment: '任务开始时间',
          },
          {
            name: 'completedAt',
            type: 'datetime',
            isNullable: true,
            comment: '任务完成时间',
          },
          {
            name: 'expiredAt',
            type: 'datetime',
            isNullable: true,
            comment: '任务过期时间',
          },
          {
            name: 'errorMessage',
            type: 'text',
            isNullable: true,
            comment: '错误信息',
          },
          {
            name: 'rawResponse',
            type: 'json',
            isNullable: true,
            comment: '原始API响应数据',
          },
          {
            name: 'tenantId',
            type: 'varchar',
            length: '36',
            default: "'0'",
            comment: '租户ID',
          },
          {
            name: 'createdAt',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
            comment: '创建时间',
          },
          {
            name: 'updatedAt',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            comment: '更新时间',
          },
        ],
      }),
      true,
    );

    // 创建索引
    await queryRunner.createIndex(
      'document_translation_tasks',
      new TableIndex({
        name: 'IDX_document_translation_tasks_external_task_id',
        columnNames: ['externalTaskId'],
      }),
    );

    await queryRunner.createIndex(
      'document_translation_tasks',
      new TableIndex({
        name: 'IDX_document_translation_tasks_account_id',
        columnNames: ['accountId'],
      }),
    );

    await queryRunner.createIndex(
      'document_translation_tasks',
      new TableIndex({
        name: 'IDX_document_translation_tasks_status',
        columnNames: ['status'],
      }),
    );

    await queryRunner.createIndex(
      'document_translation_tasks',
      new TableIndex({
        name: 'IDX_document_translation_tasks_created_at',
        columnNames: ['createdAt'],
      }),
    );

    // 创建外键约束
    await queryRunner.createForeignKey(
      'document_translation_tasks',
      new TableForeignKey({
        name: 'FK_document_translation_tasks_account_id',
        columnNames: ['accountId'],
        referencedTableName: 'accounts',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除外键约束
    await queryRunner.dropForeignKey(
      'document_translation_tasks',
      'FK_document_translation_tasks_account_id',
    );

    // 删除索引
    await queryRunner.dropIndex(
      'document_translation_tasks',
      'IDX_document_translation_tasks_external_task_id',
    );
    await queryRunner.dropIndex(
      'document_translation_tasks',
      'IDX_document_translation_tasks_account_id',
    );
    await queryRunner.dropIndex(
      'document_translation_tasks',
      'IDX_document_translation_tasks_status',
    );
    await queryRunner.dropIndex(
      'document_translation_tasks',
      'IDX_document_translation_tasks_created_at',
    );

    // 删除表
    await queryRunner.dropTable('document_translation_tasks');
  }
}
